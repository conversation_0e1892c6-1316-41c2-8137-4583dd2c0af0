# Frontend-specific files (root .gitignore handles common files)

# Dependencies
node_modules

# Build outputs
dist
dist-ssr
build

# Vite/React specific
*.local
.env.local
.env.development.local
.env.test.local
.env.production.local

# Frontend logs (specific to this directory)
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Frontend-specific editor files
.vscode/*
!.vscode/extensions.json

# Windows/Visual Studio specific
*.suo
*.ntvs*
*.njsproj
*.sln
