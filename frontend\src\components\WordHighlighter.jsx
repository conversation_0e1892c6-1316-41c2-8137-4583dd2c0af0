import { useState, useEffect, useRef } from 'react';
import { transcriptionAPI } from '../services/api';

export const WordHighlighter = ({
  mediaFile,
  transcription,
  playerRef,
  isEnabled = true,
  className = ''
}) => {
  const [wordLevelData, setWordLevelData] = useState([]);
  const [currentWordIndex, setCurrentWordIndex] = useState(-1);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const overlayRef = useRef(null);

  // Load word-level VTT data
  useEffect(() => {
    if (!mediaFile || !transcription || !transcription.has_word_level_vtt || !isEnabled) {
      return;
    }

    const loadWordLevelData = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const wordVttUrl = transcriptionAPI.getWordLevelSubtitleFileUrl(mediaFile.id);
        const response = await fetch(wordVttUrl);
        
        if (!response.ok) {
          throw new Error('Failed to load word-level subtitle data');
        }
        
        const vttText = await response.text();
        const parsedData = parseWordLevelVTT(vttText);
        setWordLevelData(parsedData);
      } catch (err) {
        console.error('Error loading word-level data:', err);
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    };

    loadWordLevelData();
  }, [mediaFile, transcription, isEnabled]);

  // Set up time update listener
  useEffect(() => {
    if (!playerRef?.current || !wordLevelData.length || !isEnabled) {
      return;
    }

    const handleTimeUpdate = () => {
      const currentTime = playerRef.current.currentTime();
      const activeWordIndex = findActiveWordIndex(currentTime, wordLevelData);
      
      if (activeWordIndex !== currentWordIndex) {
        setCurrentWordIndex(activeWordIndex);
      }
    };

    const player = playerRef.current;
    player.on('timeupdate', handleTimeUpdate);

    return () => {
      player.off('timeupdate', handleTimeUpdate);
    };
  }, [playerRef, wordLevelData, currentWordIndex, isEnabled]);

  // Parse VTT content to extract word-level timing
  const parseWordLevelVTT = (vttText) => {
    const lines = vttText.split('\n');
    const words = [];
    let i = 0;

    while (i < lines.length) {
      const line = lines[i].trim();
      
      // Skip WEBVTT header and empty lines
      if (line === 'WEBVTT' || line === '') {
        i++;
        continue;
      }

      // Check if this is a cue number
      if (/^\d+$/.test(line)) {
        i++; // Move to timing line
        
        if (i < lines.length) {
          const timingLine = lines[i].trim();
          const timingMatch = timingLine.match(/(\d{2}:\d{2}:\d{2}\.\d{3}) --> (\d{2}:\d{2}:\d{2}\.\d{3})/);
          
          if (timingMatch) {
            const startTime = parseVTTTime(timingMatch[1]);
            const endTime = parseVTTTime(timingMatch[2]);
            
            i++; // Move to text line
            if (i < lines.length) {
              const textLine = lines[i].trim();
              
              // Extract word from VTT cue (remove speaker labels and styling)
              const wordMatch = textLine.match(/<c\.word-highlight>(.*?)<\/c>/);
              const word = wordMatch ? wordMatch[1] : textLine.replace(/<[^>]*>/g, '');
              
              if (word) {
                words.push({
                  word: word.trim(),
                  start: startTime,
                  end: endTime,
                  index: words.length
                });
              }
            }
          }
        }
      }
      i++;
    }

    return words;
  };

  // Convert VTT time format to seconds
  const parseVTTTime = (timeString) => {
    const [hours, minutes, seconds] = timeString.split(':');
    return parseInt(hours) * 3600 + parseInt(minutes) * 60 + parseFloat(seconds);
  };

  // Find the currently active word based on playback time
  const findActiveWordIndex = (currentTime, words) => {
    for (let i = 0; i < words.length; i++) {
      if (currentTime >= words[i].start && currentTime <= words[i].end) {
        return i;
      }
    }
    return -1;
  };

  // Get current segment text with highlighted words
  const getCurrentSegmentWithHighlights = () => {
    if (!transcription?.segments || currentWordIndex === -1) {
      return null;
    }

    const currentWord = wordLevelData[currentWordIndex];
    if (!currentWord) return null;

    // Find the segment that contains this word
    const currentSegment = transcription.segments.find(segment => 
      currentWord.start >= segment.start && currentWord.end <= segment.end
    );

    if (!currentSegment) return null;

    // Get all words in this segment
    const segmentWords = wordLevelData.filter(word => 
      word.start >= currentSegment.start && word.end <= currentSegment.end
    );

    // Create highlighted text
    let highlightedText = currentSegment.text;
    
    // Replace each word with highlighted version if it's the current word
    segmentWords.forEach(word => {
      if (word.index === currentWordIndex) {
        const regex = new RegExp(`\\b${escapeRegExp(word.word)}\\b`, 'gi');
        highlightedText = highlightedText.replace(regex, `<span class="current-word-highlight">${word.word}</span>`);
      }
    });

    return {
      segment: currentSegment,
      highlightedText,
      currentWord
    };
  };

  // Escape special regex characters
  const escapeRegExp = (string) => {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  };

  if (!isEnabled || !transcription?.has_word_level_vtt) {
    return null;
  }

  if (isLoading) {
    return (
      <div className={`word-highlighter ${className}`}>
        <div className="flex items-center justify-center p-4">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-sm text-gray-600">Loading word highlighting...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`word-highlighter ${className}`}>
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-sm text-red-600">Error loading word highlighting: {error}</p>
        </div>
      </div>
    );
  }

  const segmentData = getCurrentSegmentWithHighlights();

  return (
    <div className={`word-highlighter ${className}`} ref={overlayRef}>
      {segmentData && (
        <div className="bg-white/95 backdrop-blur-sm rounded-lg p-4 shadow-lg border">
          <div className="text-sm text-gray-500 mb-2">
            Current Segment ({segmentData.segment.start.toFixed(1)}s - {segmentData.segment.end.toFixed(1)}s)
          </div>
          <div 
            className="text-lg leading-relaxed"
            dangerouslySetInnerHTML={{ __html: segmentData.highlightedText }}
          />
          {segmentData.currentWord && (
            <div className="text-xs text-blue-600 mt-2">
              Current word: "{segmentData.currentWord.word}" 
              ({segmentData.currentWord.start.toFixed(2)}s - {segmentData.currentWord.end.toFixed(2)}s)
            </div>
          )}
        </div>
      )}
      
      {/* Debug info (can be removed in production) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-2 text-xs text-gray-400">
          Words loaded: {wordLevelData.length} | Current word index: {currentWordIndex}
        </div>
      )}
    </div>
  );
};

export default WordHighlighter;
