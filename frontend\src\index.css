@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import 'video.js/dist/video-js.css';
@import './styles/esl-player.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base styles */
@layer base {
  body {
    font-family: 'Inter', system-ui, sans-serif;
    @apply bg-gray-50 text-gray-900;
  }

  * {
    @apply border-gray-200;
  }
}

/* Custom component styles */
@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .input-field {
    @apply block w-full rounded-lg border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500;
  }

  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
  }

  .word-highlight {
    @apply bg-yellow-200 text-yellow-900 px-1 rounded;
  }

  .word-highlight-active {
    @apply bg-yellow-400 text-yellow-900 px-1 rounded font-semibold;
  }
}

/* Video player container */
.video-player-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  background: #000;
  border-radius: 0.5rem;
  overflow: hidden;
}

.video-player-container .video-js {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 0.5rem;
}

/* Video.js theme customization */
.video-js {
  font-family: 'Inter', system-ui, sans-serif;
}

.video-js .vjs-big-play-button {
  background-color: rgba(37, 99, 235, 0.9);
  border: none;
  border-radius: 50%;
  width: 80px;
  height: 80px;
  line-height: 80px;
  font-size: 2rem;
  top: 50%;
  left: 50%;
  margin-top: -40px;
  margin-left: -40px;
  transition: all 0.3s ease;
}

.video-js .vjs-big-play-button:hover {
  background-color: rgba(37, 99, 235, 1);
  transform: scale(1.1);
}

.video-js .vjs-control-bar {
  background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.8) 100%);
  height: 4rem;
}

.video-js .vjs-progress-control {
  height: 0.5rem;
}

.video-js .vjs-play-progress {
  background-color: #2563eb;
}

.video-js .vjs-volume-level {
  background-color: #2563eb;
}

/* Custom subtitle styling */
.vjs-text-track-display {
  pointer-events: none;
}

.vjs-text-track-cue {
  background-color: rgba(0, 0, 0, 0.8) !important;
  color: white !important;
  font-size: 1.2em !important;
  line-height: 1.4 !important;
  padding: 0.2em 0.5em !important;
  border-radius: 0.25rem !important;
}

/* ESL controls styling */
.esl-controls {
  @apply flex flex-wrap gap-2 mt-4 p-4 bg-gray-50 rounded-lg;
}

.esl-controls button {
  @apply px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200;
}

.esl-controls button:disabled {
  @apply opacity-50 cursor-not-allowed;
}

/* Transcript panel styles moved to esl-player.css */

/* Upload progress */
.upload-progress {
  @apply w-full bg-gray-200 rounded-full h-2;
}

.upload-progress-bar {
  @apply bg-primary-600 h-2 rounded-full transition-all duration-300;
}
