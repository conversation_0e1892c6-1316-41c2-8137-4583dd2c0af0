# Generated by Django 5.2.1 on 2025-05-27 23:44

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ChunkUpload',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('upload_id', models.UUIDField(default=uuid.uuid4)),
                ('chunk_number', models.IntegerField()),
                ('total_chunks', models.IntegerField()),
                ('chunk_size', models.IntegerField()),
                ('filename', models.CharField(max_length=255)),
                ('file_type', models.CharField(max_length=10)),
                ('total_size', models.BigIntegerField()),
                ('chunk_file', models.FileField(upload_to='temp_chunks/')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('is_assembled', models.BooleanField(default=False)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'indexes': [models.Index(fields=['upload_id'], name='media_files_upload__347d9e_idx'), models.Index(fields=['user', '-uploaded_at'], name='media_files_user_id_2bb8e0_idx')],
                'unique_together': {('upload_id', 'chunk_number')},
            },
        ),
        migrations.CreateModel(
            name='MediaFile',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('filename_original', models.CharField(max_length=255)),
                ('filesize_bytes', models.BigIntegerField()),
                ('file_type', models.CharField(choices=[('video', 'Video'), ('audio', 'Audio')], max_length=10)),
                ('mime_type', models.CharField(max_length=100)),
                ('upload_date', models.DateTimeField(auto_now_add=True)),
                ('duration_seconds', models.IntegerField(blank=True, null=True)),
                ('language_transcription', models.CharField(choices=[('en', 'English'), ('es', 'Spanish'), ('fr', 'French'), ('de', 'German'), ('ja', 'Japanese')], default='en', max_length=10)),
                ('status', models.CharField(choices=[('pending_upload', 'Pending Upload'), ('uploading', 'Uploading'), ('uploaded_processing_assembly', 'Processing Assembly'), ('processing_audio', 'Processing Audio'), ('pending_transcription', 'Pending Transcription'), ('transcribing', 'Transcribing'), ('completed', 'Completed'), ('failed_upload', 'Failed Upload'), ('failed_assembly', 'Failed Assembly'), ('failed_extraction', 'Failed Audio Extraction'), ('failed_transcription', 'Failed Transcription'), ('failed_audio_too_large', 'Failed - Audio Too Large')], default='pending_upload', max_length=30)),
                ('replicate_job_id', models.CharField(blank=True, max_length=100, null=True)),
                ('storage_path_original', models.CharField(blank=True, max_length=512, null=True)),
                ('storage_path_audio', models.CharField(blank=True, max_length=512, null=True)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='media_files', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-upload_date'],
                'indexes': [models.Index(fields=['user', '-upload_date'], name='media_files_user_id_b3ea9b_idx'), models.Index(fields=['status'], name='media_files_status_76a340_idx')],
            },
        ),
    ]
